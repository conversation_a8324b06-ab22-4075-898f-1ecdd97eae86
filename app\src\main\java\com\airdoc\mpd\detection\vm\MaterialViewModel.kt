package com.airdoc.mpd.detection.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.mpd.detection.bean.MaterialLibraryDto
import com.airdoc.mpd.detection.repository.MaterialRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: MaterialViewModel
 * Author by <PERSON><PERSON><PERSON><PERSON><PERSON>,Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 素材库ViewModel
 */
class MaterialViewModel : ViewModel() {

    companion object {
        private val TAG = MaterialViewModel::class.java.name
        
        // 素材库更新成功
        const val MATERIAL_UPDATE_SUCCESS = "MATERIAL_UPDATE_SUCCESS"
    }

    private val materialRepository by lazy { MaterialRepository() }

    // 素材库列表
    val materialLibrariesLiveData = MutableLiveData<List<MaterialLibraryDto>?>()
    
    // 素材库更新结果
    val materialUpdateLiveData = MutableLiveData<Any?>()

    /**
     * 获取有效素材库列表
     */
    fun listMaterialLibraries() {
        viewModelScope.launch {
            MutableStateFlow(materialRepository.listMaterialLibraries()).collectResponse {
                onSuccess = { it, _, _ ->
                    Logger.d(TAG, msg = "listMaterialLibraries onSuccess")
                    materialLibrariesLiveData.postValue(it)
                    materialUpdateLiveData.postValue(MATERIAL_UPDATE_SUCCESS)
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "listMaterialLibraries onDataEmpty")
                    materialLibrariesLiveData.postValue(emptyList())
                    materialUpdateLiveData.postValue(MATERIAL_UPDATE_SUCCESS)
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "listMaterialLibraries onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    materialLibrariesLiveData.postValue(null)
                    materialUpdateLiveData.postValue(Pair(errorCode, errorMsg))
                }
                onError = {
                    Logger.e(TAG, msg = "listMaterialLibraries onError = $it")
                    materialLibrariesLiveData.postValue(null)
                    materialUpdateLiveData.postValue(null)
                }
            }
        }
    }
}
