# 素材库管理系统

## 概述

这是一个完整的素材库管理系统，支持从服务器获取素材库信息、下载ZIP文件、解压到本地目录、并提供便捷的前端访问接口。

## 系统架构

### 核心组件

1. **MaterialApiService** - API接口服务
2. **MaterialRepository** - 数据仓库
3. **MaterialViewModel** - 视图模型
4. **MaterialManager** - 素材库管理器
5. **MaterialDownloader** - 下载器
6. **MaterialHelper** - 前端辅助工具

### 数据流程

```
API调用 → 获取素材库列表 → 下载ZIP文件 → 解压到本地 → 更新映射文件 → 前端访问
```

## 功能特性

### 1. 自动版本更新
- 点击"更新"按钮调用API获取最新素材库信息
- 自动下载第一个素材库（如果本地不存在）
- 成功后更新版本号显示

### 2. 文件管理
- 素材库按ID存储在独立文件夹中
- 支持文件完整性校验（SHA256）
- 自动清理临时文件

### 3. 映射管理
- JSON格式存储素材库映射信息
- 内存缓存提高访问性能
- 支持增量更新

### 4. 前端访问
- 提供多种图片加载方式
- 支持图片缩放和优化
- 统计信息和完整性检查

## 使用方法

### 1. 在Activity中使用

```kotlin
class YourActivity : AppCompatActivity() {
    private val materialViewModel by viewModels<MaterialViewModel>()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 监听更新结果
        materialViewModel.materialUpdateLiveData.observe(this) { result ->
            when (result) {
                MaterialViewModel.MATERIAL_UPDATE_SUCCESS -> {
                    // 更新成功
                }
                MaterialViewModel.MATERIAL_DOWNLOAD_SUCCESS -> {
                    // 下载完成
                }
                is Pair<*, *> -> {
                    // 错误处理
                }
            }
        }
        
        // 获取素材库列表
        materialViewModel.listMaterialLibraries()
    }
}
```

### 2. 获取素材库图片

```kotlin
// 获取所有图片信息
val images = MaterialHelper.getMaterialImages(context, materialId)

// 获取图片文件路径
val imagePaths = MaterialHelper.getMaterialImagePaths(context, materialId)

// 加载图片到ImageView
MaterialUsageExample.loadMaterialImageToImageView(
    context, materialId, "image.jpg", imageView
)

// 批量加载图片
val bitmaps = MaterialUsageExample.loadAllMaterialImages(context, materialId)
```


### 3. 素材库管理

```kotlin
// 检查素材库是否存在
val exists = MaterialHelper.isMaterialExists(context, materialId)

// 获取素材库信息
val info = MaterialHelper.getMaterialInfo(materialId)

// 获取统计信息
val stats = MaterialUsageExample.getMaterialStatistics(context, materialId)

// 删除素材库
val success = MaterialHelper.deleteMaterial(context, materialId)
```

## 目录结构

```
/data/data/com.airdoc.mpd/files/materials/
├── material_mapping.json          # 素材库映射文件
├── 1/                            # 素材库ID为1的文件夹
│   ├── image1.jpg
│   ├── image2.png
│   └── ...
├── 2/                            # 素材库ID为2的文件夹
│   ├── photo1.jpg
│   └── ...
└── temp_materials/               # 临时下载目录（在cache中）
    └── material_1_timestamp.zip
```

## 映射文件格式

```json
{
  "1": {
    "id": 1,
    "name": "这是一个测试素材库",
    "description": "这个一个数据库描述",
    "version": "1.0.0",
    "localPath": "/data/data/com.airdoc.mpd/files/materials/1",
    "downloadTime": 1691234567890,
    "fileCount": 25,
    "checksum": "925eed5080d3cf46f37252c7eab6c367dc1b5d8d96beee45836a58aa3cde083e"
  }
}
```

## API接口

### 获取素材库列表
```
GET /api/material/libraries
```

响应示例：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "这是一个测试素材库",
      "description": "这个一个数据库描述",
      "version": "1.0.0",
      "zipUrl": "https://assets.babyeye.com/mpd/material/oasis-v1.0.0.zip",
      "size": 189488265,
      "checksum": "925eed5080d3cf46f37252c7eab6c367dc1b5d8d96beee45836a58aa3cde083e"
    }
  ]
}
```

## 错误处理

系统提供完善的错误处理机制：

1. **网络错误** - 自动重试和错误提示
2. **文件校验失败** - 自动删除损坏文件
3. **解压失败** - 清理临时文件
4. **存储空间不足** - 提前检查和提示

## 性能优化

1. **内存缓存** - 素材库映射信息缓存在内存中
2. **图片缩放** - 支持按需缩放减少内存占用
3. **异步处理** - 下载和解压在后台线程执行
4. **增量更新** - 只下载本地不存在的素材库

## 注意事项

1. 确保应用有足够的存储权限
2. 下载大文件时注意网络状态
3. 定期清理不需要的素材库释放空间
4. 在主线程中访问UI相关的LiveData观察者

## 扩展功能

系统设计支持以下扩展：

1. **多语言素材库** - 根据语言设置下载不同素材
2. **增量更新** - 支持素材库版本比较和增量下载
3. **缓存策略** - 可配置的缓存大小和清理策略
4. **加密支持** - 支持加密的素材库文件
